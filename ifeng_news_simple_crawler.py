#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凤凰网新闻爬虫 - 简化版
直接分析和调用后端API，无需模拟浏览器
专门处理"查看更多"按钮背后的真实API接口
"""

import requests
import json
import time
import csv
import re
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import sys


class IfengNewsSimpleCrawler:
    """凤凰网新闻爬虫 - 简化版"""
    
    def __init__(self, max_news=100, get_detail=False):
        """
        初始化爬虫
        :param max_news: 最大新闻数量
        :param get_detail: 是否获取详细内容
        """
        self.base_url = "https://news.ifeng.com/"
        self.max_news = max_news
        self.get_detail = get_detail
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://news.ifeng.com/',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.news_list = []
    
    def analyze_page_for_api(self):
        """分析页面找到真实的API接口"""
        print("正在分析凤凰网页面，寻找API接口...")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            response.raise_for_status()
            
            # 查找页面中的JavaScript代码，寻找API接口
            content = response.text
            
            # 常见的API接口模式
            api_patterns = [
                r'https?://[^"\']*api[^"\']*news[^"\']*',
                r'https?://[^"\']*shankapi[^"\']*',
                r'https?://[^"\']*ifeng[^"\']*api[^"\']*',
                r'/api/[^"\']*news[^"\']*',
                r'ajax[^"\']*url[^"\']*:[^"\']*["\']([^"\']*)["\']',
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if 'news' in match.lower() or 'list' in match.lower():
                        found_apis.add(match)
            
            print(f"在页面中找到可能的API接口: {list(found_apis)}")
            return list(found_apis)
            
        except Exception as e:
            print(f"分析页面失败: {e}")
            return []
    
    def get_real_api_url(self, page_offset=0):
        """
        获取真实的API接口URL
        基于您提供的真实API接口：
        https://shankapi.ifeng.com/api/_/getColumnInfo/_/dynamicFragment/7347719280965722236/1751836110000/20/3-35191-/getColumnInfoCallback
        """
        import time

        # 当前时间戳（毫秒）
        timestamp = int(time.time() * 1000)

        # 基础参数
        base_id = "7347719280965722236"  # 这个ID可能需要动态获取
        timestamp_param = "1751836110000"  # 这个时间戳参数
        page_size = 20
        category = "3-35191-"  # 新闻分类
        callback_name = "getColumnInfoCallback"

        # 构建API URL
        api_url = (
            f"https://shankapi.ifeng.com/api/_/getColumnInfo/_/dynamicFragment/"
            f"{base_id}/{timestamp_param}/{page_size}/{category}/"
            f"{callback_name}?callback={callback_name}&_={timestamp + page_offset}"
        )

        return api_url

    def try_real_api(self):
        """尝试真实的API接口"""
        print("使用真实的凤凰网API接口...")

        try:
            api_url = self.get_real_api_url()
            print(f"测试真实API: {api_url}")

            headers = self.headers.copy()
            headers.update({
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://news.ifeng.com/',
                'Sec-Fetch-Dest': 'script',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'same-site',
            })

            response = self.session.get(api_url, headers=headers, timeout=10)
            print(f"API响应状态: {response.status_code}")

            if response.status_code == 200:
                # 处理JSONP响应
                content = response.text
                print(f"响应内容长度: {len(content)}")

                # 提取JSON数据（去掉JSONP包装）
                if content.startswith('getColumnInfoCallback(') and content.endswith(')'):
                    json_str = content[len('getColumnInfoCallback('):-1]
                    try:
                        data = json.loads(json_str)
                        print(f"✓ 成功解析JSON数据")
                        print(f"数据结构: {self.describe_data_structure(data)}")

                        if self.validate_real_api_response(data):
                            print(f"✓ API数据验证成功")
                            return True
                        else:
                            print(f"✗ API数据验证失败")
                    except json.JSONDecodeError as e:
                        print(f"✗ JSON解析失败: {e}")
                else:
                    print(f"✗ 不是预期的JSONP格式")
            else:
                print(f"✗ API请求失败")

        except Exception as e:
            print(f"✗ API测试异常: {e}")

        return False
    
    def validate_real_api_response(self, data):
        """验证真实API响应是否包含新闻数据"""
        if not isinstance(data, dict):
            return False

        # 检查真实API的数据结构
        if 'code' in data and data['code'] == 0:
            if 'data' in data and isinstance(data['data'], dict):
                if 'newsstream' in data['data'] and isinstance(data['data']['newsstream'], list):
                    newsstream = data['data']['newsstream']
                    if len(newsstream) > 0:
                        # 检查第一个新闻项目
                        first_item = newsstream[0]
                        if isinstance(first_item, dict) and 'title' in first_item and 'url' in first_item:
                            return True
        return False
    
    def describe_data_structure(self, data):
        """描述数据结构"""
        if isinstance(data, dict):
            keys = list(data.keys())
            return f"dict with keys: {keys[:5]}{'...' if len(keys) > 5 else ''}"
        elif isinstance(data, list):
            return f"list with {len(data)} items"
        else:
            return f"{type(data).__name__}"
    
    def fetch_news_from_real_api(self, max_requests=10):
        """从真实API获取新闻数据"""
        print(f"使用真实API获取新闻数据...")

        all_news = []
        request_count = 0
        last_id = None  # 用于分页的最后一个新闻ID

        while len(all_news) < self.max_news and request_count < max_requests:
            request_count += 1

            # 获取API URL，使用不同的偏移量来模拟分页
            api_url = self.get_real_api_url(page_offset=request_count * 1000)
            print(f"第 {request_count} 次请求: {api_url[:100]}...")

            try:
                headers = self.headers.copy()
                headers.update({
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Referer': 'https://news.ifeng.com/',
                    'Sec-Fetch-Dest': 'script',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-site',
                })

                response = self.session.get(api_url, headers=headers, timeout=10)

                if response.status_code == 200:
                    # 处理JSONP响应
                    content = response.text

                    if content.startswith('getColumnInfoCallback(') and content.endswith(')'):
                        json_str = content[len('getColumnInfoCallback('):-1]
                        data = json.loads(json_str)

                        news_items = self.parse_real_api_news(data)

                        if not news_items:
                            print(f"第 {request_count} 次请求没有新闻数据，停止获取")
                            break

                        # 去重
                        existing_urls = {item['url'] for item in all_news}
                        new_items = [item for item in news_items if item['url'] not in existing_urls]

                        if not new_items:
                            print(f"第 {request_count} 次请求没有新的新闻")
                            # 但不立即停止，可能下一次请求会有新数据
                            if request_count >= 3:  # 连续3次没有新数据才停止
                                break
                        else:
                            all_news.extend(new_items)
                            print(f"第 {request_count} 次请求获取到 {len(new_items)} 条新新闻，总计 {len(all_news)} 条")

                        # 检查是否已结束
                        if isinstance(data, dict) and data.get('data', {}).get('isEnd', False):
                            print("API返回isEnd=true，已获取所有数据")
                            break

                        time.sleep(2)  # 避免请求过快

                    else:
                        print(f"第 {request_count} 次请求返回格式不正确")
                        break

                else:
                    print(f"第 {request_count} 次请求失败，状态码: {response.status_code}")
                    break

            except Exception as e:
                print(f"第 {request_count} 次请求异常: {e}")
                break

        return all_news[:self.max_news]
    
    def parse_real_api_news(self, data):
        """解析真实API返回的新闻数据"""
        news_items = []

        try:
            if not isinstance(data, dict) or data.get('code') != 0:
                print("API返回错误或格式不正确")
                return news_items

            # 获取新闻流数据
            newsstream = data.get('data', {}).get('newsstream', [])
            print(f"API返回 {len(newsstream)} 条新闻")

            for item in newsstream:
                if not isinstance(item, dict):
                    continue

                # 提取标题
                title = item.get('title', '').strip()

                # 提取URL
                url = item.get('url', '').strip()

                # 提取时间
                time_str = item.get('newsTime', '').strip()

                # 提取来源
                source = item.get('source', '').strip()
                if not source:
                    source = "凤凰网"

                # 提取其他信息
                news_id = item.get('id', '')
                comment_url = item.get('commentUrl', '')
                summary = item.get('summary', '')

                if title and url:
                    news_item = {
                        'id': news_id,
                        'title': title,
                        'url': url,
                        'time': time_str,
                        'source': source,
                        'content': summary,  # 使用summary作为初始内容
                        'comment_url': comment_url
                    }
                    news_items.append(news_item)

        except Exception as e:
            print(f"解析真实API数据失败: {e}")

        return news_items
    
    def crawl_news(self):
        """执行新闻爬取"""
        print(f"开始爬取凤凰网新闻，目标数量: {self.max_news}")

        # 1. 尝试真实的API接口
        if self.try_real_api():
            print("✓ 真实API验证成功，开始获取新闻数据...")
            news_list = self.fetch_news_from_real_api()
        else:
            print("✗ 真实API验证失败，尝试其他方法...")
            return []

        # 2. 获取详细内容（如果需要）
        if self.get_detail and news_list:
            print(f"开始获取 {len(news_list)} 条新闻的详细内容...")
            for i, item in enumerate(news_list):
                print(f"获取详细内容 {i+1}/{len(news_list)}: {item['title'][:30]}...")
                detailed_content = self.get_article_content(item['url'])
                if detailed_content:
                    item['content'] = detailed_content
                time.sleep(0.5)

        self.news_list = news_list
        print(f"爬取完成！共获取 {len(news_list)} 条新闻")
        return news_list
    
    def get_article_content(self, url):
        """获取文章详细内容"""
        if not self.get_detail:
            return ""
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 凤凰网文章内容选择器
            selectors = ['.main_content', '.content', '.article-content', '.text_content']
            
            for selector in selectors:
                content_div = soup.select_one(selector)
                if content_div:
                    return content_div.get_text(strip=True)[:2000]
            
            return ""
            
        except Exception as e:
            return ""
    
    def save_to_csv(self, filename=None):
        """保存到CSV文件"""
        if not self.news_list:
            return None

        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ifeng_news_simple_{timestamp}.csv"

        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '新闻ID', '标题', '链接', '时间', '来源', '内容', '评论链接']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for i, item in enumerate(self.news_list, 1):
                    writer.writerow({
                        '序号': i,
                        '新闻ID': item.get('id', ''),
                        '标题': item['title'],
                        '链接': item['url'],
                        '时间': item['time'],
                        '来源': item['source'],
                        '内容': item['content'],
                        '评论链接': item.get('comment_url', '')
                    })

            print(f"数据已保存到: {filename}")
            return filename

        except Exception as e:
            print(f"保存失败: {e}")
            return None
    
    def crawl(self):
        """执行完整的爬取流程"""
        news_list = self.crawl_news()
        if news_list:
            filename = self.save_to_csv()
            return filename, len(news_list)
        return None, 0


def main():
    """主函数"""
    max_news = 50
    get_detail = False
    
    if len(sys.argv) > 1:
        try:
            max_news = int(sys.argv[1])
        except ValueError:
            print("错误：新闻数量必须是数字")
            return
    
    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']
    
    print(f"启动参数: 新闻数量={max_news}, 获取详细内容={'是' if get_detail else '否'}")
    
    crawler = IfengNewsSimpleCrawler(max_news=max_news, get_detail=get_detail)
    filename, total_count = crawler.crawl()
    
    if filename and total_count > 0:
        print(f"\n爬取成功！")
        print(f"文件: {filename}")
        print(f"总数: {total_count} 条新闻")
        print(f"\n使用说明：")
        print(f"python ifeng_news_simple_crawler.py [新闻数量] [是否获取详细内容]")
        print(f"例如：")
        print(f"  python ifeng_news_simple_crawler.py 100 false  # 100条新闻标题")
        print(f"  python ifeng_news_simple_crawler.py 50 true   # 50条新闻含内容")
    else:
        print("爬取失败！请检查网络连接或API接口变化")


if __name__ == "__main__":
    main()
