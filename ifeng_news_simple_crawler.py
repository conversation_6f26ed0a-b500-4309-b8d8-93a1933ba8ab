#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凤凰网新闻爬虫 - 简化版
直接分析和调用后端API，无需模拟浏览器
专门处理"查看更多"按钮背后的真实API接口
"""

import requests
import json
import time
import csv
import re
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import sys


class IfengNewsSimpleCrawler:
    """凤凰网新闻爬虫 - 简化版"""
    
    def __init__(self, max_news=100, get_detail=False):
        """
        初始化爬虫
        :param max_news: 最大新闻数量
        :param get_detail: 是否获取详细内容
        """
        self.base_url = "https://news.ifeng.com/"
        self.max_news = max_news
        self.get_detail = get_detail
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://news.ifeng.com/',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.news_list = []
    
    def analyze_page_for_api(self):
        """分析页面找到真实的API接口"""
        print("正在分析凤凰网页面，寻找API接口...")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            response.raise_for_status()
            
            # 查找页面中的JavaScript代码，寻找API接口
            content = response.text
            
            # 常见的API接口模式
            api_patterns = [
                r'https?://[^"\']*api[^"\']*news[^"\']*',
                r'https?://[^"\']*shankapi[^"\']*',
                r'https?://[^"\']*ifeng[^"\']*api[^"\']*',
                r'/api/[^"\']*news[^"\']*',
                r'ajax[^"\']*url[^"\']*:[^"\']*["\']([^"\']*)["\']',
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if 'news' in match.lower() or 'list' in match.lower():
                        found_apis.add(match)
            
            print(f"在页面中找到可能的API接口: {list(found_apis)}")
            return list(found_apis)
            
        except Exception as e:
            print(f"分析页面失败: {e}")
            return []
    
    def try_common_api_patterns(self):
        """尝试常见的API接口模式"""
        print("尝试常见的凤凰网API接口模式...")
        
        # 基于凤凰网常见的API模式
        api_templates = [
            "https://shankapi.ifeng.com/season/news/newslist?page={}&size=20",
            "https://shankapi.ifeng.com/season/news/newslist?page={}&size=30&type=1",
            "https://api.ifeng.com/news/list?page={}&pagesize=20",
            "https://news.ifeng.com/api/more?page={}&limit=20",
            "https://news.ifeng.com/listapi/pc/news/list?page={}",
        ]
        
        successful_apis = []
        
        for template in api_templates:
            api_url = template.format(1)  # 测试第一页
            try:
                print(f"测试API: {api_url}")
                
                headers = self.headers.copy()
                headers.update({
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                })
                
                response = self.session.get(api_url, headers=headers, timeout=10)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data and self.validate_api_response(data):
                            print(f"  ✓ API有效，返回数据结构: {self.describe_data_structure(data)}")
                            successful_apis.append(template)
                        else:
                            print(f"  ✗ API返回数据无效")
                    except json.JSONDecodeError:
                        print(f"  ✗ 返回的不是JSON格式")
                else:
                    print(f"  ✗ 请求失败")
                    
            except Exception as e:
                print(f"  ✗ 请求异常: {e}")
        
        print(f"找到 {len(successful_apis)} 个有效的API接口")
        return successful_apis
    
    def validate_api_response(self, data):
        """验证API响应是否包含新闻数据"""
        if not isinstance(data, dict):
            return False
        
        # 检查常见的数据结构
        possible_keys = ['data', 'list', 'items', 'news', 'result']
        for key in possible_keys:
            if key in data:
                items = data[key]
                if isinstance(items, list) and len(items) > 0:
                    # 检查第一个项目是否像新闻数据
                    first_item = items[0]
                    if isinstance(first_item, dict):
                        news_keys = ['title', 'url', 'link', 'newsTitle', 'newsUrl']
                        if any(k in first_item for k in news_keys):
                            return True
        return False
    
    def describe_data_structure(self, data):
        """描述数据结构"""
        if isinstance(data, dict):
            keys = list(data.keys())
            return f"dict with keys: {keys[:5]}{'...' if len(keys) > 5 else ''}"
        elif isinstance(data, list):
            return f"list with {len(data)} items"
        else:
            return f"{type(data).__name__}"
    
    def fetch_news_from_api(self, api_template, max_pages=10):
        """从API获取新闻数据"""
        print(f"使用API获取新闻: {api_template}")
        
        all_news = []
        page = 1
        
        while len(all_news) < self.max_news and page <= max_pages:
            api_url = api_template.format(page)
            print(f"获取第 {page} 页: {api_url}")
            
            try:
                headers = self.headers.copy()
                headers.update({
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                })
                
                response = self.session.get(api_url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    news_items = self.parse_api_news(data)
                    
                    if not news_items:
                        print(f"第 {page} 页没有新闻数据，停止获取")
                        break
                    
                    # 去重
                    existing_urls = {item['url'] for item in all_news}
                    new_items = [item for item in news_items if item['url'] not in existing_urls]
                    
                    if not new_items:
                        print(f"第 {page} 页没有新的新闻，停止获取")
                        break
                    
                    all_news.extend(new_items)
                    print(f"第 {page} 页获取到 {len(new_items)} 条新新闻，总计 {len(all_news)} 条")
                    
                    page += 1
                    time.sleep(1)  # 避免请求过快
                    
                else:
                    print(f"第 {page} 页请求失败，状态码: {response.status_code}")
                    break
                    
            except Exception as e:
                print(f"第 {page} 页请求异常: {e}")
                break
        
        return all_news[:self.max_news]
    
    def parse_api_news(self, data):
        """解析API返回的新闻数据"""
        news_items = []
        
        try:
            # 尝试不同的数据结构
            items = []
            if isinstance(data, dict):
                for key in ['data', 'list', 'items', 'news', 'result']:
                    if key in data and isinstance(data[key], list):
                        items = data[key]
                        break
            elif isinstance(data, list):
                items = data
            
            for item in items:
                if not isinstance(item, dict):
                    continue
                
                # 提取标题
                title = ""
                for key in ['title', 'newsTitle', 'name', 'headline']:
                    if key in item and item[key]:
                        title = str(item[key]).strip()
                        break
                
                # 提取URL
                url = ""
                for key in ['url', 'link', 'newsUrl', 'href']:
                    if key in item and item[key]:
                        url = str(item[key]).strip()
                        break
                
                # 确保URL是完整的
                if url and not url.startswith('http'):
                    if url.startswith('//'):
                        url = 'https:' + url
                    elif url.startswith('/'):
                        url = 'https://news.ifeng.com' + url
                
                # 提取时间
                time_str = ""
                for key in ['time', 'publishTime', 'createTime', 'newsTime', 'date']:
                    if key in item and item[key]:
                        time_str = str(item[key]).strip()
                        break
                
                # 提取来源
                source = ""
                for key in ['source', 'media', 'author', 'from']:
                    if key in item and item[key]:
                        source = str(item[key]).strip()
                        break
                
                if not source:
                    source = "凤凰网"
                
                if title and url:
                    news_item = {
                        'title': title,
                        'url': url,
                        'time': time_str,
                        'source': source,
                        'content': ''
                    }
                    news_items.append(news_item)
                    
        except Exception as e:
            print(f"解析API数据失败: {e}")
        
        return news_items
    
    def crawl_news(self):
        """执行新闻爬取"""
        print(f"开始爬取凤凰网新闻，目标数量: {self.max_news}")
        
        # 1. 尝试常见的API接口
        successful_apis = self.try_common_api_patterns()
        
        if not successful_apis:
            print("没有找到有效的API接口，尝试分析页面...")
            # 2. 如果没找到，分析页面寻找API
            found_apis = self.analyze_page_for_api()
            # 这里可以进一步处理找到的API
            print("页面分析完成，但需要手动验证API接口")
            return []
        
        # 3. 使用第一个有效的API获取新闻
        api_template = successful_apis[0]
        news_list = self.fetch_news_from_api(api_template)
        
        # 4. 获取详细内容（如果需要）
        if self.get_detail and news_list:
            print(f"开始获取 {len(news_list)} 条新闻的详细内容...")
            for i, item in enumerate(news_list):
                print(f"获取详细内容 {i+1}/{len(news_list)}: {item['title'][:30]}...")
                item['content'] = self.get_article_content(item['url'])
                time.sleep(0.5)
        
        self.news_list = news_list
        print(f"爬取完成！共获取 {len(news_list)} 条新闻")
        return news_list
    
    def get_article_content(self, url):
        """获取文章详细内容"""
        if not self.get_detail:
            return ""
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 凤凰网文章内容选择器
            selectors = ['.main_content', '.content', '.article-content', '.text_content']
            
            for selector in selectors:
                content_div = soup.select_one(selector)
                if content_div:
                    return content_div.get_text(strip=True)[:2000]
            
            return ""
            
        except Exception as e:
            return ""
    
    def save_to_csv(self, filename=None):
        """保存到CSV文件"""
        if not self.news_list:
            return None
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ifeng_news_simple_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '标题', '链接', '时间', '来源', '内容']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for i, item in enumerate(self.news_list, 1):
                    writer.writerow({
                        '序号': i,
                        '标题': item['title'],
                        '链接': item['url'],
                        '时间': item['time'],
                        '来源': item['source'],
                        '内容': item['content']
                    })
            
            print(f"数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存失败: {e}")
            return None
    
    def crawl(self):
        """执行完整的爬取流程"""
        news_list = self.crawl_news()
        if news_list:
            filename = self.save_to_csv()
            return filename, len(news_list)
        return None, 0


def main():
    """主函数"""
    max_news = 50
    get_detail = False
    
    if len(sys.argv) > 1:
        try:
            max_news = int(sys.argv[1])
        except ValueError:
            print("错误：新闻数量必须是数字")
            return
    
    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']
    
    print(f"启动参数: 新闻数量={max_news}, 获取详细内容={'是' if get_detail else '否'}")
    
    crawler = IfengNewsSimpleCrawler(max_news=max_news, get_detail=get_detail)
    filename, total_count = crawler.crawl()
    
    if filename and total_count > 0:
        print(f"\n爬取成功！")
        print(f"文件: {filename}")
        print(f"总数: {total_count} 条新闻")
        print(f"\n使用说明：")
        print(f"python ifeng_news_simple_crawler.py [新闻数量] [是否获取详细内容]")
        print(f"例如：")
        print(f"  python ifeng_news_simple_crawler.py 100 false  # 100条新闻标题")
        print(f"  python ifeng_news_simple_crawler.py 50 true   # 50条新闻含内容")
    else:
        print("爬取失败！请检查网络连接或API接口变化")


if __name__ == "__main__":
    main()
