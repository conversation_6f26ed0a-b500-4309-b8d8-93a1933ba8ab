#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凤凰网新闻爬虫
支持爬取凤凰网新闻首页的所有新闻标题，包括处理"查看更多"按钮的动态加载
"""

import requests
import json
import time
import csv
import re
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import sys
import os


class IfengNewsCrawler:
    """凤凰网新闻爬虫"""
    
    def __init__(self, max_news=100, get_detail=False, get_all=False):
        """
        初始化爬虫
        :param max_news: 最大新闻数量
        :param get_detail: 是否获取详细内容
        :param get_all: 是否获取所有新闻（忽略max_news限制）
        """
        self.base_url = "https://news.ifeng.com/"
        self.max_news = max_news if not get_all else 9999
        self.get_detail = get_detail
        self.get_all = get_all
        
        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://news.ifeng.com/',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 存储所有新闻
        self.news_list = []
        
    def get_page_content(self, url):
        """获取页面内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except Exception as e:
            print(f"获取页面内容失败: {e}")
            return None
    
    def parse_news_from_html(self, html_content):
        """从HTML内容中解析新闻"""
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []
        
        # 查找所有新闻链接
        # 凤凰网的新闻链接通常在 <a> 标签中，href包含 /c/ 路径
        news_links = soup.find_all('a', href=re.compile(r'/c/[a-zA-Z0-9]+'))
        
        for link in news_links:
            try:
                title = link.get_text(strip=True)
                url = link.get('href')
                
                # 跳过空标题或太短的标题
                if not title or len(title) < 5:
                    continue
                
                # 构建完整URL
                if url.startswith('//'):
                    url = 'https:' + url
                elif url.startswith('/'):
                    url = urljoin(self.base_url, url)
                
                # 查找时间信息（通常在附近的元素中）
                time_text = ""
                parent = link.parent
                if parent:
                    # 查找包含时间的文本
                    time_elements = parent.find_all(text=re.compile(r'今天|昨天|\d{2}:\d{2}|\d{4}-\d{2}-\d{2}'))
                    if time_elements:
                        time_text = time_elements[0].strip()
                
                # 查找来源信息
                source = "凤凰网"
                source_elements = parent.find_all(text=True) if parent else []
                for text in source_elements:
                    if any(keyword in text for keyword in ['凤凰', '来源', '作者']):
                        source = text.strip()
                        break
                
                news_item = {
                    'title': title,
                    'url': url,
                    'time': time_text,
                    'source': source,
                    'content': ''
                }
                
                news_items.append(news_item)
                
            except Exception as e:
                print(f"解析新闻项失败: {e}")
                continue
        
        return news_items
    
    def get_more_news_api(self, page=1):
        """
        获取更多新闻的API接口
        通过分析网络请求找到真实的API接口
        """
        # 凤凰网的实际API接口（通过浏览器开发者工具分析得出）
        api_urls = [
            # 主要的新闻列表API
            f"https://shankapi.ifeng.com/season/news/newslist?page={page}&size=20&type=1",
            f"https://shankapi.ifeng.com/season/news/newslist?page={page}&size=30",
            # 备用API接口
            f"https://news.ifeng.com/api/more?page={page}&limit=20",
            f"https://api.ifeng.com/news/list?page={page}&pagesize=20",
        ]

        for api_url in api_urls:
            try:
                print(f"尝试API: {api_url}")

                # 添加更多请求头，模拟真实浏览器
                headers = self.headers.copy()
                headers.update({
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-site',
                })

                response = self.session.get(api_url, headers=headers, timeout=10)
                print(f"API响应状态: {response.status_code}")

                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"API返回数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

                        if data:
                            parsed_news = self.parse_api_response(data)
                            if parsed_news:
                                print(f"成功解析到 {len(parsed_news)} 条新闻")
                                return parsed_news
                    except json.JSONDecodeError:
                        print("API返回的不是JSON格式")
                        continue

            except Exception as e:
                print(f"API请求失败: {e}")
                continue

        print("所有API接口都无法获取数据")
        return []
    
    def parse_api_response(self, data):
        """解析API响应数据"""
        news_items = []
        
        try:
            # 根据不同的API响应格式解析
            items = []
            if 'data' in data and isinstance(data['data'], list):
                items = data['data']
            elif 'data' in data and 'list' in data['data']:
                items = data['data']['list']
            elif 'list' in data:
                items = data['list']
            
            for item in items:
                title = item.get('title', item.get('newsTitle', ''))
                url = item.get('url', item.get('newsUrl', item.get('link', '')))
                time_str = item.get('time', item.get('newsTime', item.get('publishTime', '')))
                source = item.get('source', item.get('media', '凤凰网'))
                
                if title and url:
                    # 确保URL是完整的
                    if url.startswith('//'):
                        url = 'https:' + url
                    elif url.startswith('/'):
                        url = urljoin(self.base_url, url)
                    
                    news_item = {
                        'title': title,
                        'url': url,
                        'time': time_str,
                        'source': source,
                        'content': ''
                    }
                    news_items.append(news_item)
                    
        except Exception as e:
            print(f"解析API响应失败: {e}")
        
        return news_items
    
    def get_article_content(self, url):
        """获取文章详细内容"""
        if not self.get_detail:
            return ""
        
        try:
            html = self.get_page_content(url)
            if not html:
                return ""
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # 凤凰网文章内容的常见选择器
            content_selectors = [
                '.main_content',
                '.content',
                '.article-content',
                '.text_content',
                '#main_content',
                '.main-content'
            ]
            
            content = ""
            for selector in content_selectors:
                content_div = soup.select_one(selector)
                if content_div:
                    # 移除脚本和样式标签
                    for script in content_div(["script", "style"]):
                        script.decompose()
                    
                    content = content_div.get_text(strip=True)
                    break
            
            # 如果没找到特定选择器，尝试通用方法
            if not content:
                # 查找包含大量文本的div
                divs = soup.find_all('div')
                for div in divs:
                    text = div.get_text(strip=True)
                    if len(text) > 200:  # 假设文章内容至少200字符
                        content = text
                        break
            
            return content[:2000] if content else ""  # 限制长度
            
        except Exception as e:
            print(f"获取文章内容失败 {url}: {e}")
            return ""
    
    def crawl_news(self):
        """爬取新闻"""
        print(f"开始爬取凤凰网新闻...")
        print(f"设置: 最大新闻数 {self.max_news}, 获取详细内容: {'是' if self.get_detail else '否'}")
        
        # 1. 首先获取首页内容
        print("正在获取首页新闻...")
        html_content = self.get_page_content(self.base_url)
        if html_content:
            news_items = self.parse_news_from_html(html_content)
            self.news_list.extend(news_items)
            print(f"从首页获取到 {len(news_items)} 条新闻")
        
        # 2. 尝试通过API获取更多新闻
        page = 1
        while len(self.news_list) < self.max_news and page <= 10:  # 最多尝试10页
            print(f"正在获取第 {page} 页更多新闻...")
            api_news = self.get_more_news_api(page)
            
            if not api_news:
                print(f"第 {page} 页没有更多新闻，停止获取")
                break
            
            # 去重
            existing_urls = {item['url'] for item in self.news_list}
            new_items = [item for item in api_news if item['url'] not in existing_urls]
            
            if not new_items:
                print(f"第 {page} 页没有新的新闻，停止获取")
                break
            
            self.news_list.extend(new_items)
            print(f"第 {page} 页获取到 {len(new_items)} 条新新闻")
            
            page += 1
            time.sleep(1)  # 避免请求过快
        
        # 3. 去重并限制数量
        unique_news = []
        seen_urls = set()
        for item in self.news_list:
            if item['url'] not in seen_urls and len(unique_news) < self.max_news:
                unique_news.append(item)
                seen_urls.add(item['url'])
        
        self.news_list = unique_news
        
        # 4. 获取详细内容（如果需要）
        if self.get_detail and self.news_list:
            print(f"开始获取 {len(self.news_list)} 条新闻的详细内容...")
            for i, item in enumerate(self.news_list):
                print(f"获取详细内容 {i+1}/{len(self.news_list)}: {item['title'][:30]}...")
                item['content'] = self.get_article_content(item['url'])
                time.sleep(0.5)  # 避免请求过快
        
        print(f"爬取完成！共获取 {len(self.news_list)} 条新闻")
        return self.news_list
    
    def save_to_csv(self, filename=None):
        """保存到CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ifeng_news_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '标题', '链接', '时间', '来源', '内容']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for i, item in enumerate(self.news_list, 1):
                    writer.writerow({
                        '序号': i,
                        '标题': item['title'],
                        '链接': item['url'],
                        '时间': item['time'],
                        '来源': item['source'],
                        '内容': item['content']
                    })
            
            print(f"数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return None
    
    def crawl(self):
        """执行完整的爬取流程"""
        news_list = self.crawl_news()
        if news_list:
            filename = self.save_to_csv()
            return filename, len(news_list)
        return None, 0


def main():
    """主函数"""
    # 默认参数
    max_news = 50
    get_detail = False
    get_all = False
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['all', 'a', '全部']:
            get_all = True
            max_news = 999
        else:
            try:
                max_news = int(sys.argv[1])
            except ValueError:
                print("错误：新闻数量必须是数字，或使用 'all' 获取全部新闻")
                return
    
    if len(sys.argv) > 2:
        get_detail = sys.argv[2].lower() in ['true', '1', 'yes', 'y']
    
    # 创建爬虫实例
    crawler = IfengNewsCrawler(
        max_news=max_news,
        get_detail=get_detail,
        get_all=get_all
    )
    
    # 开始爬取
    filename, total_count = crawler.crawl()
    
    if filename and total_count > 0:
        print(f"\n爬取成功！")
        print(f"文件: {filename}")
        print(f"总数: {total_count} 条新闻")
        print(f"\n使用说明：")
        print(f"python ifeng_news_crawler.py [新闻数量|all] [是否获取详细内容]")
        print(f"例如：")
        print(f"  python ifeng_news_crawler.py 50 true    # 50条新闻，包含详细内容")
        print(f"  python ifeng_news_crawler.py 100 false  # 100条新闻，不包含详细内容")
        print(f"  python ifeng_news_crawler.py all true   # 获取全部新闻，包含详细内容")
    else:
        print("爬取失败！")


if __name__ == "__main__":
    main()
